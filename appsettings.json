{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "SwaggerList": [{"Name": "Alert Management", "URL": "http://************:32045/swagger/V1/swagger.json"}, {"Name": "Decision New Generation", "URL": "http://************:32057/swagger/V1/swagger.json"}, {"Name": "IDC.Template", "URL": "http://localhost:5156/swagger/Main/swagger.json"}, {"Name": "SQLite Universal Data", "URL": "http://************:32058/swagger/V1/swagger.json"}, {"Name": "Workflow Runner", "URL": "http://************:32064/swagger/V1/swagger.json"}], "OtherSettings": {"image_path": "", "SyncWfConfigIntoMongo": false}, "DefaultConStrings": {"WFRunner": {"SQLite": "memory", "MongoDB": "local", "PGSQL": "ConnectionString_en"}}, "SqLiteContextSettings": {"memory": "Data Source=:memory:;Cache=Private;Mode=Memory", "de_configs": "Data Source=/media/fadhly/Data/-Repo/TemplateAndUtility/IDC.Template/wwwroot/sqlite.db;Cache=Shared;Mode=ReadWrite"}, "DbContextSettings": {"ConnectionString_en": "User ID=idc_fadhly;Password=******;HOST=localhost;Port=5432;Database=idc.en;Pooling=true;MinPoolSize=1;MaxPoolSize=1000;"}, "MongoDBSettings": {"dev": "**************************************************", "local": "mongodb://localhost:27017", "withReplica": "*********************************************************************************************************"}, "APISettings": {"urlAPI_idccust": "http://************:30992/idccust/", "urlAPI_idclms": "http://************:30979/idclms/", "urlAPI_idcen": "http://************:30989/idcen/", "urlAPI_idcmail": "http://************:30977/idcmail/", "urlAPI_idcaudittrail": "http://************:30998/idcaudittrail/", "urlAPI_idcflows": "http://***************:30988/idcflows/", "urlAPI_idcproduct": "http://************:30971/idcproduct/", "urlAPI_idccore": "http://************:30993/idccore/", "urlAPI_idcchannel": "http://************:32001/idcchannel/", "urlAPI_idclabs": "http://************:30981/idclabs/", "urlAPI_idcprocess": "http://***************:30972/idcprocess/", "urlAPI_idcreport": "http://************:30970/idcreport/", "urlAPI_idcservice": "http://************:30968/api/", "urlAPI_idcpreint": "http://***************:32014/idcpreint/", "urlAPI_idcdeservice": "http://************:30990/idcdeservice/", "urlAPI_idcnotif": "http://************:30976/idcnotif/", "urlAPI_idcocr": "http://************:4867/idcocr/", "urlAPI_asliri": "http://************:30999/idcasliri/", "urlAPI_idcform": "http://************:30987/idcform/", "urlAPI_idcinterfaces": "http://***************:30983/idcinterfaces/", "urlAPI_idcbatch": "http://************:30997/idcbatch/", "urlAPI_idclicense": "http://************:40005/idclicense/", "urlAPI_idccampaign": "http://api1.danakini.co.id:30994/idccampaign/", "urlAPI_idckbij": "http://************:30987/idckbij/", "urlAPI_idcopenapi": "http://************:32004/idcopenapi/", "urlAPI_idccredential": "http://localhost:60365/idccredential/", "urlAPI_idcparam": "http://************:30975/idcparameter/", "urlAPI_idcims": "http://************:30984/idcims/", "urlAPI_idcdecision": "http://************:32057/api", "urlAPI_idcdeservice_local": "http://localhost:30990/idcdeservice/", "urlAPI_idcnicepay": "http://localhost:30982/idcnicepay/", "urlAPI_idcinte": "http://************:30984/idcinte/", "urlAPI_idcparameter": "http://localhost:30979/idcparameter/", "urlAPI_idcprivilege": "http://************:30973/idcprivilege/", "urlAPI_idcpefindo": "http://************:30974/idcpefindo/", "urlAPI_idcthrottledcif": "http://************:30965/idcthrottledcif/", "urlAPI_idcvkyc": "http://************:32002/idcvkyc/", "urlAPI_idcversion": "http://************:30983/idcversion/", "urlAPI_idcasliri": "http://************:30999/idcasliri/", "urlAPI_idcips": "http://************:30985/idcips/", "urlAPI_idcmodel": "http://************:8111/", "urlAPI_idcprivyid": "http:/************/:30981/idcprivyid/", "urlAPI_idcml": "http://************:31003/idcml/", "urlAPI_idc_throttle_dcif": "http://************:30965/idcThrottleDcif/", "urlAPI_integration_channel": "http://************:30968/api/", "urlWS_kawanlama": "http://***********/hcpluswsv/WSV/wsvDKI.asmx/", "urlAPI_Exec": "http://************:30990/idcdeservice/callre/Exec", "urlAPI_idcsync": "http://************:30967/idcsync/", "urlAPI_idcva": "http://************:30995/", "urlAPI_request_slik_checking_direct": "http://localhost:30974/idcpefindo/", "urlAPI_result_slik_checking_direct": "http://localhost:30974/idcpefindo/", "urlAPI_result_slik_agregation_direct": "http://cbasslik.companyname.co.id/slik/getAgregate", "urlAPI_request_slik_credit_summary": "http://cbasslik.companyname.co.id/slik/getSummary", "urlAPI_request_slik_matching": "http://cbasslik.companyname.co.id/slik/getDataMatching", "urlAPI_request_slik_mmue": "http://cbasslik.companyname.co.id/slik/getMMUE", "urlAPI_request_slik_pdf": "http://************/CBASAPI/getPDF", "urlAPI_request_slik_ideb": "http://cbasslik.companyname.co.id/slik/getIdeb", "urlAPI_slik_pdmodel_direct": "http://cbasslik.companyname.co.id/slik/getAgregate", "urlAPI_dummy": "http://************:30968/idcservice/RunningProcess/DummyAPI", "urlAPI_serverOCBC": "https://************/", "urlAPI_serverOCBC_LK": "https://cif-uat.appsnp.ocbcnisp.com/", "urlAPI_oneloanOCBC": "http://***********:8081/", "urlAPI_OverBookin7093": "http://************:8000/TRX/v1/Overbooking/TRXCreateOverbooking7093", "urlAPI_InquiryAccount": "https://************:4431/account-inquiry-be/api/v1/Account/InquiryAccountDetail", "urlAPI_oneccOCBC": "https://onecc-uat.appsnp.ocbcnisp.com/api/", "urlAPI_glcrgl": "http://************:8000/TRX/v1/overbooking/TRXCreateOverbooking7070", "urlAPI_casa1304": "http://************:8000/TRX/v1/overbooking/CAOverbookingCA1304", "urlAPI_casa1314": "http://************:8000/TRX/v1/overbooking/CAOverbookingSA1314", "urlAPI_casa2305": "http://************:8000/TRX/v1/overbooking/SAOBSANoBook2305", "urlAPI_casa2315": "http://************:8000/TRX/v1/overbooking/SAOBCANoBook2315", "urlAPI_ChannelingFee": "", "urlAPI_idclibrary": "http://localhost:30373", "urlAPI_idcdecisioncb": "http://************:32057/", "decisionParameter": "scorecard"}, "IntegrationSetting": {"cif_eligible": "GC21", "disbursed_pb": "GC15", "disbursed_skn": "GC16", "req_slik_checking": "dummy", "res_slik_checking": "dummy", "get_slik_agregation_result": "dummy", "slik_pdmodel": "dummy", "req_slik_credit_summary": "dummy", "req_slik_matching": "dummy", "req_slik_mmue": "dummy", "req_slik_pdf": "dummy", "req_slik_ideb": "dummy", "req_OverBooking7093": "direct", "req_InquiryAccount": "direct", "req_ChannelingFee": "dummy", "req_glcrgl": "direct", "req_casa1304": "direct", "req_casa1314": "direct", "req_casa2305": "direct", "req_casa2315": "direct", "req_casacrgl": "direct", "cif_eligible_throt": "GC25"}}