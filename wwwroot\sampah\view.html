<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>PGSQL Script Deploy Tools</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
  <style>
    body {
      padding: 20px;
      background-color: #f8f9fa;
    }
    .container-main {
      background: white;
      border: 1px solid #ced4da;
      border-radius: 0.375rem;
      padding: 15px 20px 20px 20px;
    }
    fieldset {
      border: 1px solid #ced4da;
      border-radius: 0.375rem;
      padding: 1rem 1.25rem 1.25rem 1.25rem;
      margin-bottom: 1rem;
      background: #fafafa;
    }
    legend {
      font-size: 1rem;
      font-weight: 600;
      width: auto;
      padding: 0 10px;
      margin-bottom: 0;
    }
    .form-label {
      font-weight: 500;
    }
    .btn-start {
      white-space: nowrap;
      margin-top: 32px;
    }
    .tab-content {
      min-height: 300px;
      border: 1px solid #ced4da;
      border-top: none;
      padding: 15px;
      background: white;
      border-bottom-left-radius: 0.375rem;
      border-bottom-right-radius: 0.375rem;
    }
    #logSection {
      margin-top: 10px;
      border: 1px solid #ced4da;
      height: 120px;
      background: white;
      overflow-y: auto;
      font-family: monospace;
      font-size: 0.875rem;
      padding: 10px;
      white-space: pre-wrap;
      border-radius: 0.375rem;
    }
  </style>
</head>
<body>

  <div class="container container-main">
    <h5 class="mb-4">PGSQL Script Deploy Tools</h5>

    <form>
      <div class="row g-3">
        <!-- Left: Deployment Configurations -->
        <div class="col-lg-6">
          <fieldset>
            <legend>Deployment Configurations</legend>

            <div class="mb-3 row align-items-center">
              <label for="connectionString" class="col-sm-4 col-form-label">Connection String :</label>
              <div class="col-sm-8 d-flex">
                <input type="text" class="form-control" id="connectionString" placeholder="Enter connection string" />
                <button type="button" class="btn btn-outline-primary ms-2 flex-shrink-0">Test Connection</button>
              </div>
            </div>
            <div class="mb-3 row">
              <label for="outputDir" class="col-sm-4 col-form-label">Output Dir :</label>
              <div class="col-sm-8 d-flex">
                <input type="text" class="form-control" id="outputDir" placeholder="Output directory path" />
                <button type="button" class="btn btn-secondary ms-2 flex-shrink-0">Browse</button>
              </div>
            </div>
            <div class="mb-0 row">
              <label for="ticketNumber" class="col-sm-4 col-form-label">Ticket Number :</label>
              <div class="col-sm-8">
                <input type="text" class="form-control" id="ticketNumber" placeholder="Enter ticket number" />
              </div>
            </div>
          </fieldset>
        </div>

        <!-- Right: SCM Configurations -->
        <div class="col-lg-6">
          <fieldset>
            <legend>SCM Configurations</legend>

            <div class="mb-3 row">
              <label for="scmUrl" class="col-sm-4 col-form-label">SCM URL :</label>
              <div class="col-sm-8">
                <input type="text" class="form-control" id="scmUrl" placeholder="Enter SCM URL" />
              </div>
            </div>

            <div class="mb-3 row">
              <label for="apiKey" class="col-sm-4 col-form-label">API Key :</label>
              <div class="col-sm-8">
                <input type="text" class="form-control" id="apiKey" placeholder="Enter API Key" />
              </div>
            </div>

            <div class="mb-3 row align-items-center">
              <label for="targetBranch" class="col-sm-4 col-form-label">Target Branch :</label>
              <div class="col-sm-8">
                <input type="text" class="form-control" id="targetBranch" placeholder="Enter target branch" />
              </div>
            </div>

            <div class="mb-0 row align-items-center">
              <div class="offset-sm-4 col-sm-8 d-flex align-items-center">
                <input class="form-check-input me-2" type="checkbox" value="" id="gitPullFirst" />
                <label class="form-check-label" for="gitPullFirst">Do GIT Pull First (DEV Branch)</label>
              </div>
            </div>
          </fieldset>
        </div>
      </div>

      <div class="d-flex justify-content-end mb-3">
        <button type="submit" class="btn btn-success btn-start">Start Deployment</button>
      </div>
    </form>

    <hr class="mt-0" />

    <!-- Tabs -->
    <ul class="nav nav-tabs" id="deployTabs" role="tablist">
      <li class="nav-item" role="presentation">
        <button class="nav-link active" id="tables-tab" data-bs-toggle="tab" data-bs-target="#tables" type="button" role="tab" aria-controls="tables" aria-selected="true">Tables</button>
      </li>
      <li class="nav-item" role="presentation">
        <button class="nav-link" id="store-functions-tab" data-bs-toggle="tab" data-bs-target="#store-functions" type="button" role="tab" aria-controls="store-functions" aria-selected="false">Store Functions</button>
      </li>
      <li class="nav-item" role="presentation">
        <button class="nav-link" id="views-tab" data-bs-toggle="tab" data-bs-target="#views" type="button" role="tab" aria-controls="views" aria-selected="false">Views</button>
      </li>
    </ul>
    <div class="tab-content" id="deployTabsContent">
      <div class="tab-pane fade show active" id="tables" role="tabpanel" aria-labelledby="tables-tab">
        <!-- Placeholder content for Tables -->
        <div class="p-2 text-muted">No tables loaded.</div>
      </div>
      <div class="tab-pane fade" id="store-functions" role="tabpanel" aria-labelledby="store-functions-tab">
        <!-- Placeholder content for Store Functions -->
        <div class="p-2 text-muted">No store functions loaded.</div>
      </div>
      <div class="tab-pane fade" id="views" role="tabpanel" aria-labelledby="views-tab">
        <!-- Placeholder content for Views -->
        <div class="p-2 text-muted">No views loaded.</div>
      </div>
    </div>

    <!-- Log Section -->
    <div id="logSection" aria-label="Log output area" tabindex="0" role="region" aria-live="polite" aria-relevant="additions text">
      lbLog
    </div>
  </div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

